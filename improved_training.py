import cv2
import numpy as np
from os import listdir
from os.path import isfile, join
import os

def improved_face_training():
    """
    Improved face recognition training with better preprocessing and validation
    """
    data_path = 'C:/Users/<USER>/OneDrive/Desktop/RATNA/dataset'
    
    if not os.path.exists(data_path):
        print(f"Error: Dataset path {data_path} does not exist!")
        return False
    
    onlyfiles = [f for f in listdir(data_path) if isfile(join(data_path, f))]
    
    if len(onlyfiles) == 0:
        print("Error: No image files found in dataset!")
        return False
    
    print(f"Found {len(onlyfiles)} images in dataset")
    
    Training_Data, Labels = [], []
    skipped_files = 0
    
    for i, file in enumerate(onlyfiles):
        image_path = join(data_path, file)
        
        # Try to read image
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if image is None:
            print(f"Warning: Unable to read {image_path}, skipping.")
            skipped_files += 1
            continue
        
        # Standardize preprocessing (same as detection)
        image = cv2.resize(image, (200, 200))
        image = cv2.equalizeHist(image)
        
        # Validate image quality
        if np.std(image) < 10:  # Too uniform (likely corrupted)
            print(f"Warning: Low variance in {file}, skipping.")
            skipped_files += 1
            continue
        
        Training_Data.append(np.asarray(image, dtype=np.uint8))
        Labels.append(i)
        
        if (i + 1) % 10 == 0:
            print(f"Processed {i + 1}/{len(onlyfiles)} images...")
    
    if len(Training_Data) == 0:
        print("Error: No valid training data found!")
        return False
    
    print(f"Training with {len(Training_Data)} images ({skipped_files} skipped)")
    
    # Convert to numpy arrays
    Labels = np.asarray(Labels, dtype=np.int32)
    
    # Create and train model
    print("Creating LBPH Face Recognizer...")
    model = cv2.face.LBPHFaceRecognizer_create(
        radius=1,        # Default radius
        neighbors=8,     # Default neighbors  
        grid_x=8,        # Default grid
        grid_y=8,        # Default grid
        threshold=80.0   # Recognition threshold
    )
    
    print("Training model...")
    model.train(Training_Data, Labels)
    
    # Save the model
    model_path = 'face_recognition_model.yml'
    model.save(model_path)
    print(f"Model saved to {model_path}")
    
    # Basic validation
    print("\nPerforming basic validation...")
    correct_predictions = 0
    total_predictions = min(10, len(Training_Data))  # Test on first 10 samples
    
    for i in range(total_predictions):
        prediction = model.predict(Training_Data[i])
        predicted_label = prediction[0]
        confidence_distance = prediction[1]
        
        if predicted_label == Labels[i]:
            correct_predictions += 1
        
        print(f"Sample {i}: Predicted={predicted_label}, Actual={Labels[i]}, Distance={confidence_distance:.2f}")
    
    accuracy = (correct_predictions / total_predictions) * 100
    print(f"\nValidation Accuracy: {accuracy:.1f}% ({correct_predictions}/{total_predictions})")
    
    if accuracy < 70:
        print("Warning: Low validation accuracy. Consider collecting more diverse training data.")
    
    print("\nTraining completed successfully!")
    print("\nRecommendations for better recognition:")
    print("1. Collect at least 50-100 images per person")
    print("2. Include various lighting conditions")
    print("3. Include different facial expressions")
    print("4. Include slight head rotations")
    print("5. Ensure good image quality (not blurry)")
    
    return True

def test_trained_model():
    """
    Test the trained model with a sample image
    """
    model_path = 'face_recognition_model.yml'
    
    if not os.path.exists(model_path):
        print("Error: No trained model found. Please run training first.")
        return False
    
    # Load the model
    model = cv2.face.LBPHFaceRecognizer_create()
    model.read(model_path)
    
    print("Model loaded successfully!")
    print("You can now use this model for live detection.")
    
    return True

if __name__ == "__main__":
    print("🚀 Improved Face Recognition Training")
    print("=" * 50)
    
    # Run training
    if improved_face_training():
        print("\n" + "=" * 50)
        test_trained_model()
    else:
        print("Training failed!")
